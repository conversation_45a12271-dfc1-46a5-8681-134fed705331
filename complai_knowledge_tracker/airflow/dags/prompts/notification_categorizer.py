"""
Prompts for RBI notification categorization and processing
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict
from enum import Enum


class NotificationCategory(str, Enum):
    """RBI notification categories"""
    AMENDMENT = "Amendment"
    SUPERSEDED = "Superseded"
    REPEALED_WITHDRAWN = "Repealed/Withdrawn"
    REVIEW = "Review"
    RELAXATION = "Relaxation"
    NEW_REGULATORY_ISSUANCE = "New_Regulatory_Issuance"
    INFORMATIONAL = "Informational"


class ConfidenceLevel(str, Enum):
    """Confidence levels for categorization"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ActionType(str, Enum):
    """Knowledge base update actions"""
    UPDATE_DOCUMENT = "UPDATE_DOCUMENT"
    REMOVE_DOCUMENT = "REMOVE_DOCUMENT"
    ADD_DOCUMENT = "ADD_DOCUMENT"
    ADD_TEMPORARY_NOTE = "ADD_TEMPORARY_NOTE"
    NO_ACTION = "NO_ACTION"


class Priority(str, Enum):
    """Action priority levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class NotificationCategorizationResult(BaseModel):
    """Result of notification categorization analysis"""
    category: NotificationCategory = Field(..., description="Notification category")
    confidence: ConfidenceLevel = Field(..., description="Confidence level of categorization")
    reasoning: str = Field(..., description="Brief explanation of categorization")
    affects_regulations: bool = Field(..., description="Whether this affects regulatory documents")
    keywords_found: List[str] = Field(default_factory=list, description="Relevant keywords found")
    requires_kb_update: bool = Field(..., description="Whether knowledge base update is required")



class DocumentAction(BaseModel):
    """A document and its required action with enhanced metadata"""
    document_id: str = Field(..., description="Primary document identifier (reference number or title)")
    action_type: str = Field(..., description="Required action: UPDATE_DOCUMENT, REMOVE_DOCUMENT, ADD_DOCUMENT, ADD_TEMPORARY_NOTE, NO_ACTION")
    confidence: str = Field(..., description="Confidence level: high, medium, low")
    document_title: Optional[str] = Field(None, description="Full document title if available")
    reference_number: Optional[str] = Field(None, description="Official reference number if different from document_id")
    department: Optional[str] = Field(None, description="Issuing department (DBOD, DPSS, DOR, etc.)")
    original_date: Optional[str] = Field(None, description="Original issuance date if mentioned (YYYY-MM-DD format)")
    reasoning: Optional[str] = Field(None, description="Brief explanation for why this action is required")

class AffectedDocumentsResult(BaseModel):
    """Result of affected documents extraction with their update actions"""
    document_actions: List[DocumentAction] = Field(default_factory=list, description="List of documents and their required actions")
    document_keywords: List[str] = Field(default_factory=list, description="Keywords for document search")
    has_new_document_link: bool = Field(False, description="Whether notification contains new document links")
    new_document_url: str = Field("", description="URL of new document if available")
    rbi_links: List[str] = Field(default_factory=list, description="All RBI links found in notification")
    processing_notes: str = Field("", description="Additional processing notes and observations")
    requires_manual_review: bool = Field(False, description="Whether manual review is recommended")
    superseded_documents: List[str] = Field(default_factory=list, description="Documents being superseded/replaced")
    amendment_details: Optional[str] = Field(None, description="Specific details about amendments if applicable")
    effective_date: Optional[str] = Field(None, description="Effective date of changes if mentioned (YYYY-MM-DD format)")


class UpdateAction(BaseModel):
    """Specific action to update the knowledge base"""
    action_type: ActionType = Field(..., description="Type of action to perform")
    target_document: str = Field(..., description="Document identifier")
    priority: Priority = Field(..., description="Action priority")
    details: str = Field(..., description="Specific instructions for the action")
    expiry_date: Optional[str] = Field(None, description="Expiry date if temporary (YYYY-MM-DD format)")
    new_document_url: Optional[str] = Field(None, description="URL of new document if applicable")
    rbi_page_url: Optional[str] = Field(None, description="RBI page URL if found in description")


class UpdateActionResult(BaseModel):
    """Result of update action determination"""
    actions: List[UpdateAction] = Field(default_factory=list, description="List of actions to perform")
    processing_notes: str = Field("", description="Additional notes for processing")
    requires_manual_review: bool = Field(False, description="Whether manual review is required")

NOTIFICATION_CATEGORIZER_PROMPT = """
You are an expert in RBI (Reserve Bank of India) regulations and notifications. Your task is to analyze RBI notification content and categorize it based on its impact on the regulatory knowledge base.

**NOTIFICATION CATEGORIES:**
1. **Amendment** - Introduces changes to existing regulatory documents (modifying or adding rules)
2. **Superseded** - A new regulation/direction replaces an older one entirely  
3. **Repealed/Withdrawn** - An existing regulation/circular is removed and no longer in effect
4. **Review** - Comprehensive review leading to updates or consolidation of guidelines
5. **Relaxation** - Provides leniency on existing rules (can be temporary or permanent)
6. **New_Regulatory_Issuance** - Completely new regulation or direction (doesn't supersede existing)
7. **Informational** - Clarifications, operational updates, monetary policy that don't change regulation text

**ANALYSIS INSTRUCTIONS:**
- Analyze the notification title and content carefully
- Look for keywords like "Amendment", "Supersede", "Withdraw", "Repeal", "Review", "Relaxation", "New", etc.
- Consider the impact on existing regulations in the knowledge base
- Determine if this affects regulatory documents or is purely informational

**INPUT:**
Title: {title}
RSS Description: {content}
Link: {link}

**NOTE:** The RSS Description contains structured HTML content with the notification details including circular numbers, dates, addressees, and main content.

**OUTPUT FORMAT:**
Return a NotificationCategorizationResult object.
"""

AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT = """
You are an expert in RBI regulatory document analysis and knowledge base management. Your task is to identify ALL regulatory documents affected by this notification, including documents that are being amended, superseded, replaced, or withdrawn.

**CRITICAL INSTRUCTIONS:**
1. **IDENTIFY ALL AFFECTED DOCUMENTS:**
   - Documents explicitly mentioned by reference number (e.g., "DBOD.No.Leg.BC.21/09.07.007/2002-03")
   - Documents mentioned by title (e.g., "Master Direction on Credit Card Operations")
   - Documents being amended, superseded, replaced, or withdrawn
   - Parent documents that contain the sections being modified
   - Related circulars or guidelines that may be impacted

2. **EXTRACT COMPREHENSIVE DOCUMENT IDENTIFIERS:**
   - Full reference numbers in formats like "DBOD.No.Leg.BC.21/09.07.007/2002-03", "RBI/2024-25/123"
   - Document titles including "Master Direction", "Circular", "Guidelines"
   - Dates of original issuance when mentioned
   - Department codes (DBOD, DPSS, DOR, etc.)

3. **DETERMINE PRECISE ACTIONS:**
   - **UPDATE_DOCUMENT**: For amendments, modifications, or updates to existing documents
   - **REMOVE_DOCUMENT**: For documents being repealed, withdrawn, or superseded completely
   - **ADD_DOCUMENT**: For entirely new regulatory documents being issued
   - **ADD_TEMPORARY_NOTE**: For temporary relaxations or time-bound measures
   - **NO_ACTION**: Only when explicitly stated no changes are needed

4. **SPECIAL ATTENTION TO:**
   - Amendment notifications: Find the ORIGINAL document being amended
   - Supersession notifications: Identify BOTH the old document (to remove) AND new document (to add)
   - Withdrawal notifications: Find ALL documents being withdrawn
   - Consolidation notifications: Identify ALL documents being consolidated

**NOTIFICATION CONTENT:**
Title: {title}
RSS Description: {content}
Category: {category}

**CONFIDENCE LEVELS:**
- **high**: Document clearly identified with specific reference number or unambiguous title
- **medium**: Document identified but may need verification (partial reference or general title)
- **low**: Document inferred from context but requires manual review

**EXAMPLES OF DOCUMENT IDENTIFICATION:**
- "DBOD.No.Leg.BC.21/09.07.007/2002-03 dated March 15, 2003" →
  document_id: "DBOD.No.Leg.BC.21/09.07.007/2002-03", reference_number: "DBOD.No.Leg.BC.21/09.07.007/2002-03", department: "DBOD", original_date: "2003-03-15"
- "Master Direction on Credit Card Operations for Banks" →
  document_id: "Master Direction - Credit Card Operations", document_title: "Master Direction on Credit Card Operations for Banks"
- "Circular No. 123 on KYC Guidelines" →
  document_id: "Circular No. 123 - KYC Guidelines", document_title: "Circular No. 123 on KYC Guidelines"

**IMPORTANT EXTRACTION RULES:**
1. Always extract the most specific document identifier available
2. If a notification amends "paragraph 5.2 of Master Direction XYZ", the affected document is "Master Direction XYZ"
3. For supersession notifications, identify BOTH old documents (REMOVE_DOCUMENT) and new documents (ADD_DOCUMENT)
4. Extract all document metadata available (titles, reference numbers, departments, dates)
5. Include reasoning for each action to help downstream processing
6. Identify superseded documents separately in the superseded_documents field
7. Extract effective dates when mentioned for proper timing of updates

**OUTPUT REQUIREMENTS:**
- Fill all available fields in DocumentAction objects
- Provide comprehensive document_keywords for search
- Include processing_notes with key observations
- Set requires_manual_review=true for complex or ambiguous cases
- Extract amendment_details and effective_date when available

**OUTPUT FORMAT:**
Return an AffectedDocumentsResult object with comprehensive document_actions and metadata.
"""

UPDATE_ACTION_DETERMINER_PROMPT = """
You are an expert in RBI regulatory knowledge base management. Based on the notification analysis, determine the specific actions needed to update the knowledge base.

**NOTIFICATION ANALYSIS:**
Title: {title}
Category: {category}
Affected Documents: {affected_documents}
RSS Description: {content}

**NOTE:** The RSS Description contains the structured notification content. Use this to understand the specific changes being made.

**ACTION TYPES AND REQUIREMENTS:**

1. **UPDATE_DOCUMENT** - Fetch latest version and replace existing document
   - Use when: Existing documents are being amended, modified, or updated
   - Required fields: target_document, details
   - Optional fields: rbi_page_url (if RBI page URL is mentioned)

2. **REMOVE_DOCUMENT** - Delete document from active knowledge base
   - Use when: Documents are being repealed, withdrawn, or superseded completely
   - Required fields: target_document, details
   - Optional fields: None

3. **ADD_DOCUMENT** - Add new document to knowledge base
   - Use when: Entirely new regulatory documents are being issued
   - Required fields: target_document, details, new_document_url
   - **CRITICAL**: For ADD_DOCUMENT actions, you MUST provide new_document_url
   - Extract new_document_url from:
     * Direct PDF links in the RSS description (look for .pdf URLs)
     * RBI page URLs that contain the new document (set as rbi_page_url)
     * If no direct PDF link found, extract the RBI page URL and set as rbi_page_url
   - If no URL can be found, do NOT create an ADD_DOCUMENT action

4. **ADD_TEMPORARY_NOTE** - Add temporary relaxation/note (with expiry)
   - Use when: Temporary relaxations or time-bound measures are announced
   - Required fields: target_document, details, expiry_date
   - Optional fields: None

5. **NO_ACTION** - No knowledge base changes needed
   - Use when: Notification is purely informational with no regulatory changes

**URL EXTRACTION GUIDELINES:**
- Look for direct PDF links ending in .pdf in the RSS description
- Look for RBI page URLs (typically starting with https://www.rbi.org.in/)
- For ADD_DOCUMENT actions, prefer direct PDF links as new_document_url
- If only RBI page URL is available, set it as rbi_page_url and the system will extract the PDF
- If no URLs are found, consider if the action should be NO_ACTION instead

**OUTPUT FORMAT:**
Return an UpdateActionResult object with properly populated actions including all required fields.
"""
