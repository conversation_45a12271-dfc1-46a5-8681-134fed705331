"""
Prompts for RBI notification categorization and processing
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict
from enum import Enum


class NotificationCategory(str, Enum):
    """RBI notification categories"""
    AMENDMENT = "Amendment"
    SUPERSEDED = "Superseded"
    REPEALED_WITHDRAWN = "Repealed/Withdrawn"
    REVIEW = "Review"
    RELAXATION = "Relaxation"
    NEW_REGULATORY_ISSUANCE = "New_Regulatory_Issuance"
    INFORMATIONAL = "Informational"


class ConfidenceLevel(str, Enum):
    """Confidence levels for categorization"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class ActionType(str, Enum):
    """Knowledge base update actions"""
    UPDATE_DOCUMENT = "UPDATE_DOCUMENT"
    REMOVE_DOCUMENT = "REMOVE_DOCUMENT"
    ADD_DOCUMENT = "ADD_DOCUMENT"
    ADD_TEMPORARY_NOTE = "ADD_TEMPORARY_NOTE"
    NO_ACTION = "NO_ACTION"


class Priority(str, Enum):
    """Action priority levels"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


class NotificationCategorizationResult(BaseModel):
    """Result of notification categorization analysis"""
    category: NotificationCategory = Field(..., description="Notification category")
    confidence: ConfidenceLevel = Field(..., description="Confidence level of categorization")
    reasoning: str = Field(..., description="Brief explanation of categorization")
    affects_regulations: bool = Field(..., description="Whether this affects regulatory documents")
    keywords_found: List[str] = Field(default_factory=list, description="Relevant keywords found")
    requires_kb_update: bool = Field(..., description="Whether knowledge base update is required")



class DocumentAction(BaseModel):
    """A document and its required action with enhanced metadata"""
    document_id: str = Field(..., description="Primary document identifier (reference number or title)")
    action_type: str = Field(..., description="Required action: UPDATE_DOCUMENT, REMOVE_DOCUMENT, ADD_DOCUMENT, ADD_TEMPORARY_NOTE, NO_ACTION")
    confidence: str = Field(..., description="Confidence level: high, medium, low")
    document_title: Optional[str] = Field(None, description="Full document title if available")
    reference_number: Optional[str] = Field(None, description="Official reference number if different from document_id")
    department: Optional[str] = Field(None, description="Issuing department (DBOD, DPSS, DOR, etc.)")
    original_date: Optional[str] = Field(None, description="Original issuance date if mentioned (YYYY-MM-DD format)")
    update_location: Optional[str] = Field(None, description="Specific location of update (paragraph number, section, or 'full-document')")
    sunset_withdraw_date: Optional[str] = Field(None, description="Date when document will be withdrawn due to sunset clause (YYYY-MM-DD format)")
    reasoning: Optional[str] = Field(None, description="Brief explanation for why this action is required")

class AffectedDocumentsResult(BaseModel):
    """Result of affected documents extraction with their update actions"""
    document_actions: List[DocumentAction] = Field(default_factory=list, description="List of documents and their required actions")
    document_keywords: List[str] = Field(default_factory=list, description="Keywords for document search")
    has_new_document_link: bool = Field(False, description="Whether notification contains new document links")
    new_document_url: str = Field("", description="URL of new document if available")
    rbi_links: List[str] = Field(default_factory=list, description="All RBI links found in notification")
    processing_notes: str = Field("", description="Additional processing notes and observations")
    requires_manual_review: bool = Field(False, description="Whether manual review is recommended")
    superseded_documents: List[str] = Field(default_factory=list, description="Documents being superseded/replaced")
    amendment_details: Optional[str] = Field(None, description="Specific details about amendments if applicable")
    effective_date: Optional[str] = Field(None, description="Effective date of changes if mentioned (YYYY-MM-DD format)")


class UpdateAction(BaseModel):
    """Specific action to update the knowledge base"""
    action_type: ActionType = Field(..., description="Type of action to perform")
    target_document: str = Field(..., description="Document identifier")
    priority: Priority = Field(..., description="Action priority")
    details: str = Field(..., description="Specific instructions for the action")
    expiry_date: Optional[str] = Field(None, description="Expiry date if temporary (YYYY-MM-DD format)")
    new_document_url: Optional[str] = Field(None, description="URL of new document if applicable")
    rbi_page_url: Optional[str] = Field(None, description="RBI page URL if found in description")


class UpdateActionResult(BaseModel):
    """Result of update action determination"""
    actions: List[UpdateAction] = Field(default_factory=list, description="List of actions to perform")
    processing_notes: str = Field("", description="Additional notes for processing")
    requires_manual_review: bool = Field(False, description="Whether manual review is required")

NOTIFICATION_CATEGORIZER_PROMPT = """
You are an expert in RBI (Reserve Bank of India) regulations and notifications. Your task is to analyze RBI notification content and categorize it based on its impact on the regulatory knowledge base.

**NOTIFICATION CATEGORIES:**
1. **Amendment** - Introduces changes to existing regulatory documents (modifying or adding rules)
2. **Superseded** - A new regulation/direction replaces an older one entirely  
3. **Repealed/Withdrawn** - An existing regulation/circular is removed and no longer in effect
4. **Review** - Comprehensive review leading to updates or consolidation of guidelines
5. **Relaxation** - Provides leniency on existing rules (can be temporary or permanent)
6. **New_Regulatory_Issuance** - Completely new regulation or direction (doesn't supersede existing)
7. **Informational** - Clarifications, operational updates, monetary policy that don't change regulation text

**ANALYSIS INSTRUCTIONS:**
- Analyze the notification title and content carefully
- Look for keywords like "Amendment", "Supersede", "Withdraw", "Repeal", "Review", "Relaxation", "New", etc.
- Consider the impact on existing regulations in the knowledge base
- Determine if this affects regulatory documents or is purely informational

**INPUT:**
Title: {title}
RSS Description: {content}
Link: {link}

**NOTE:** The RSS Description contains structured HTML content with the notification details including circular numbers, dates, addressees, and main content.

**OUTPUT FORMAT:**
Return a NotificationCategorizationResult object.
"""

AFFECTED_DOCUMENTS_EXTRACTOR_PROMPT = """
You are an expert in RBI regulatory document analysis and knowledge base management. Your task is to identify ALL regulatory documents affected by this notification with enhanced metadata extraction and pattern recognition.

**ENHANCED PATTERN RECOGNITION:**

1. **AMENDMENT DETECTION** - Look for these EXACT phrases:
   - "reference is invited to the circular" → Extract ALL <a> tags in that sentence
   - "reference is invited to the master direction" → Extract document details
   - "in supersession of" → Identify BOTH old (REMOVE) and new (ADD) documents
   - "in partial modification of" → UPDATE_DOCUMENT with specific update_location

2. **WITHDRAWAL/REPEAL DETECTION** - Look for these EXACT phrases:
   - "will stand withdrawn" / "shall stand withdrawn" → REMOVE_DOCUMENT
   - "Previous Guidelines superseded" → REMOVE_DOCUMENT
   - "hereby repealed" / "stand repealed" → REMOVE_DOCUMENT
   - "List of Circulars repealed" → Extract ALL documents from the list

3. **SUNSET CLAUSE DETECTION** - Look for:
   - "sunset clause of one year" → Calculate sunset_withdraw_date as next July 1st
   - "valid for a period of one year" → Set expiry date
   - "shall cease to be operative" → Set withdrawal date

4. **COMPREHENSIVE METADATA EXTRACTION:**

   **MANDATORY FIELDS:**
   - **reference_number**: Extract from patterns like "DBOD.No.Leg.BC.21/09.07.007/2002-03", "RBI/2024-25/134"
   - **original_date**: Look for "dated [date]", parse to YYYY-MM-DD format
   - **document_id**: Use reference_number if available, otherwise descriptive title
   - **action_type**: Based on detected patterns above

   **ENHANCED FIELDS:**
   - **update_location**:
     * Look for "paragraph X", "para X", "section X.Y", "clause X"
     * If specific location found, record it (e.g., "paragraph 5.2")
     * Otherwise, set as "full-document"
   - **sunset_withdraw_date**:
     * For sunset clauses, calculate as "YYYY-07-01" (next July 1st)
     * For temporary measures, extract expiry date
   - **department**: Extract from reference (DBOD, DPSS, DOR, FMRD, etc.)
   - **document_title**: Full title when mentioned

5. **ANCHOR TEXT PROCESSING** - CRITICAL:
   - Process HTML content BEFORE any tag stripping
   - Extract ALL <a> tags with href and text content
   - Reference numbers are often embedded in anchor text
   - Preserve link relationships for document identification

6. **CONFIDENCE SCORING RULES:**
   - **HIGH**: Exact pattern match + complete reference number + clear action type
   - **MEDIUM**: Pattern match + partial reference OR clear title + identifiable action
   - **LOW**: Inferred from context + missing critical metadata

7. **VALIDATION REQUIREMENTS:**
   - UPDATE_DOCUMENT and REMOVE_DOCUMENT actions MUST have reference_number
   - All actions SHOULD have original_date when mentioned in notification
   - Flag requires_manual_review=true if critical fields missing
   - Block processing if validation fails for high-impact actions

**NOTIFICATION CONTENT:**
Title: {title}
RSS Description: {content}
Category: {category}

**ENHANCED EXAMPLES OF DOCUMENT IDENTIFICATION:**

1. **Amendment with Reference:**
   Input: "reference is invited to the circular <a href='...'>DBOD.No.Leg.BC.21/09.07.007/2002-03</a> dated March 15, 2003"
   Output: document_id="DBOD.No.Leg.BC.21/09.07.007/2002-03", reference_number="DBOD.No.Leg.BC.21/09.07.007/2002-03",
           department="DBOD", original_date="2003-03-15", action_type="UPDATE_DOCUMENT", confidence="high"

2. **Withdrawal with Sunset:**
   Input: "Master Direction on Import of Goods... sunset clause of one year... will stand withdrawn"
   Output: document_id="Master Direction - Import of Goods", action_type="REMOVE_DOCUMENT",
           sunset_withdraw_date="2025-07-01", confidence="high"

3. **Paragraph Amendment:**
   Input: "paragraph 5.2 of Master Direction on Credit Card Operations is amended"
   Output: document_id="Master Direction - Credit Card Operations", action_type="UPDATE_DOCUMENT",
           update_location="paragraph 5.2", confidence="high"

4. **Supersession (Dual Action):**
   Input: "Master Direction XYZ supersedes circular ABC dated..."
   Output: [
     {document_id="Circular ABC", action_type="REMOVE_DOCUMENT", confidence="high"},
     {document_id="Master Direction XYZ", action_type="ADD_DOCUMENT", confidence="high"}
   ]

5. **Repealed List:**
   Input: "List of Circulars repealed: 1. DBOD.No.123... 2. DPSS.No.456..."
   Output: Multiple REMOVE_DOCUMENT actions for each listed circular

**CRITICAL PROCESSING RULES:**
1. **HTML Processing Order**: Extract anchors FIRST, then process text content
2. **Batch Operations**: Handle multiple documents in single notifications
3. **Date Calculations**: For sunset clauses, always calculate next July 1st
4. **Validation Gates**: Block processing if UPDATE/REMOVE actions lack reference_number
5. **Confidence Thresholds**: Require HIGH confidence for automated processing
6. **Manual Review Triggers**: Complex supersessions, missing metadata, ambiguous references

**OUTPUT REQUIREMENTS:**
- Complete all enhanced metadata fields (update_location, sunset_withdraw_date)
- Provide detailed reasoning for each action
- Include comprehensive processing_notes
- Set requires_manual_review=true for validation failures
- Extract ALL available document metadata

**OUTPUT FORMAT:**
Return an AffectedDocumentsResult object with enhanced DocumentAction objects containing all new metadata fields.
"""

UPDATE_ACTION_DETERMINER_PROMPT = """
You are an expert in RBI regulatory knowledge base management. Based on the notification analysis, determine the specific actions needed to update the knowledge base.

**NOTIFICATION ANALYSIS:**
Title: {title}
Category: {category}
Affected Documents: {affected_documents}
RSS Description: {content}

**NOTE:** The RSS Description contains the structured notification content. Use this to understand the specific changes being made.

**ACTION TYPES AND REQUIREMENTS:**

1. **UPDATE_DOCUMENT** - Fetch latest version and replace existing document
   - Use when: Existing documents are being amended, modified, or updated
   - Required fields: target_document, details
   - Optional fields: rbi_page_url (if RBI page URL is mentioned)

2. **REMOVE_DOCUMENT** - Delete document from active knowledge base
   - Use when: Documents are being repealed, withdrawn, or superseded completely
   - Required fields: target_document, details
   - Optional fields: None

3. **ADD_DOCUMENT** - Add new document to knowledge base
   - Use when: Entirely new regulatory documents are being issued
   - Required fields: target_document, details, new_document_url
   - **CRITICAL**: For ADD_DOCUMENT actions, you MUST provide new_document_url
   - Extract new_document_url from:
     * Direct PDF links in the RSS description (look for .pdf URLs)
     * RBI page URLs that contain the new document (set as rbi_page_url)
     * If no direct PDF link found, extract the RBI page URL and set as rbi_page_url
   - If no URL can be found, do NOT create an ADD_DOCUMENT action

4. **ADD_TEMPORARY_NOTE** - Add temporary relaxation/note (with expiry)
   - Use when: Temporary relaxations or time-bound measures are announced
   - Required fields: target_document, details, expiry_date
   - Optional fields: None

5. **NO_ACTION** - No knowledge base changes needed
   - Use when: Notification is purely informational with no regulatory changes

**URL EXTRACTION GUIDELINES:**
- Look for direct PDF links ending in .pdf in the RSS description
- Look for RBI page URLs (typically starting with https://www.rbi.org.in/)
- For ADD_DOCUMENT actions, prefer direct PDF links as new_document_url
- If only RBI page URL is available, set it as rbi_page_url and the system will extract the PDF
- If no URLs are found, consider if the action should be NO_ACTION instead

**OUTPUT FORMAT:**
Return an UpdateActionResult object with properly populated actions including all required fields.
"""

# Enhanced Pattern Matching Prompts

ENHANCED_NOTIFICATION_ANALYZER_PROMPT = """
You are an expert in RBI (Reserve Bank of India) regulatory document analysis with deep knowledge of notification patterns and document relationships. Your task is to perform comprehensive analysis of RBI notification content to identify all affected documents and determine precise actions.

**CRITICAL PATTERN RECOGNITION:**

1. **AMENDMENT PATTERNS** - Look for these exact phrases:
   - "reference is invited to the circular" / "reference is invited to circular"
   - "reference is invited to the master direction"
   - "reference is invited to the notification"
   - When found, extract ALL anchor links (<a> tags) in the same sentence or paragraph

2. **WITHDRAWAL/SUPERSESSION PATTERNS** - Look for these exact phrases:
   - "will stand withdrawn" / "shall stand withdrawn"
   - "Previous Guidelines superseded" / "previous guidelines superseded"
   - "hereby superseded" / "are hereby superseded"
   - "repealed and superseded" / "withdrawn and superseded"

3. **SUNSET CLAUSE PATTERNS** - Look for:
   - "sunset clause of one year" followed by date calculations
   - "valid for a period of one year" with automatic withdrawal
   - Calculate withdrawal date as July 1st of the following year

4. **REPEALED DOCUMENT LISTS** - Look for:
   - "List of Circulars repealed" / "List of circulars repealed"
   - "Appendix" containing tables of repealed documents
   - "The following circulars stand repealed"
   - Extract ALL document references from such lists

5. **ANCHOR TEXT EXTRACTION** - Before any HTML processing:
   - Capture ALL <a> tags with their text content and href attributes
   - Look for reference numbers inside anchor text
   - Preserve link relationships for document identification

**ENHANCED METADATA EXTRACTION:**

1. **Reference Numbers** (MANDATORY):
   - Extract from anchor text: "DBOD.No.Leg.BC.21/09.07.007/2002-03"
   - Extract from text: "RBI/2024-25/134"
   - Extract department codes: DBOD, DPSS, DOR, FMRD, etc.

2. **Original Dates**:
   - Look for "dated [date]" patterns
   - Extract from PDF headers if mentioned
   - Parse various date formats to YYYY-MM-DD

3. **Update Location**:
   - Look for "paragraph X", "para X", "section X.Y"
   - If specific location mentioned, record it
   - Otherwise, mark as "full-document"

4. **Sunset Withdrawal Dates**:
   - When sunset clause detected, calculate next July 1st
   - Format as YYYY-MM-DD

**CONFIDENCE SCORING:**
- **HIGH**: Exact pattern match + complete reference number + clear action
- **MEDIUM**: Pattern match + partial reference + identifiable document
- **LOW**: Inferred from context + missing critical details

**VALIDATION REQUIREMENTS:**
- UPDATE_DOCUMENT and REMOVE_DOCUMENT actions MUST have reference_number
- All actions MUST have original_date when available
- Flag for manual review if critical fields missing

**INPUT:**
Title: {title}
RSS Description: {content}
Link: {link}

**SPECIAL INSTRUCTIONS:**
1. Process HTML content BEFORE stripping tags to capture anchor information
2. Look for multiple documents in single notifications
3. Handle batch operations (multiple repeals, multiple amendments)
4. Extract ALL available metadata fields
5. Provide detailed reasoning for each action

**OUTPUT FORMAT:**
Return an AffectedDocumentsResult object with comprehensive document_actions, enhanced metadata, and processing notes.
"""

PATTERN_VALIDATION_PROMPT = """
You are an expert validator for RBI notification processing. Your task is to validate and enhance the extracted document actions by applying advanced pattern recognition and ensuring data completeness.

**VALIDATION CHECKLIST:**

1. **Reference Number Validation**:
   - Verify format: DEPT.No.XXX.YY.ZZZ/AA.BB.CC/YYYY-YY or RBI/YYYY-YY/NNN
   - Check for missing reference numbers in UPDATE/REMOVE actions
   - Flag incomplete or malformed references

2. **Date Validation**:
   - Ensure dates are in YYYY-MM-DD format
   - Validate date logic (effective dates after issue dates)
   - Check for missing critical dates

3. **Action Type Validation**:
   - Verify action matches notification content
   - Check for missing ADD_DOCUMENT actions when new PDFs are mentioned
   - Validate REMOVE_DOCUMENT actions have proper justification

4. **Confidence Adjustment**:
   - Upgrade to HIGH confidence when exact patterns match
   - Downgrade to MEDIUM/LOW when critical fields missing
   - Flag for manual review when validation fails

5. **Completeness Check**:
   - Ensure all mentioned documents are captured
   - Check for batch operations (multiple documents in lists)
   - Verify sunset clause calculations

**INPUT:**
Original Analysis: {original_analysis}
Notification Content: {content}
Title: {title}

**OUTPUT:**
Return validated and enhanced AffectedDocumentsResult with confidence adjustments and validation notes.
"""
