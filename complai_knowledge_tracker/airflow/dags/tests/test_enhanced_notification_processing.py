"""
Unit tests for enhanced notification processing pipeline
Tests the five key notification types mentioned in the executive summary
"""

import pytest
import json
from unittest.mock import Mock, patch
from bs4 import BeautifulSoup

# Test fixtures for the five key notification types

# 1. Amendment notification with "reference is invited to..."
AMENDMENT_NOTIFICATION_HTML = """
<p>July 08, 2025</p>
<p>All Scheduled Commercial Banks (excluding Regional Rural Banks)</p>
<p>Madam / Sir,</p>
<p><strong>Aadhaar-based Authentication for Card Present Transactions</strong></p>
<p>A reference is invited to the circular <a href="https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=11566">DPSS.CO.PD.No.892/02.14.003/2016-17</a> dated September 15, 2016 on the captioned subject.</p>
<p>2. In order to enhance security in card present transactions, it has been decided to allow Aadhaar-based authentication as an additional factor of authentication for such transactions.</p>
<p>3. The provisions of this circular shall come into effect from January 1, 2026.</p>
<p>Yours faithfully,</p>
<p>(<PERSON><PERSON>)<br>Chief General Manager</p>
"""

# 2. Withdrawal notification with "will stand withdrawn"
WITHDRAWAL_NOTIFICATION_HTML = """
<p>July 08, 2025</p>
<p>All Scheduled Commercial Banks</p>
<p>Madam / Sir,</p>
<p><strong>Master Circular on Import of Goods and Services</strong></p>
<p>Please refer to the Master Circular <a href="https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=848">FEMA.23/2015-16</a> dated July 1, 2015 on Import of Goods and Services.</p>
<p>2. The above Master Circular contains a sunset clause of one year and will stand withdrawn on July 1, 2026.</p>
<p>3. All Authorised Dealer banks are advised to take note of the same.</p>
<p>Yours faithfully,</p>
<p>(A. K. Misra)<br>Chief General Manager</p>
"""

# 3. Supersession notification with "Previous Guidelines superseded"
SUPERSESSION_NOTIFICATION_HTML = """
<p>July 08, 2025</p>
<p>All Scheduled Commercial Banks</p>
<p>Madam / Sir,</p>
<p><strong>Disclosure in Financial Statements - Notes to Accounts</strong></p>
<p>Please refer to our circular <a href="https://www.rbi.org.in/Scripts/BS_ViewMasDirections.aspx?id=1281">DBOD.No.BP.BC.63/21.04.018/2015-16</a> dated February 25, 2016.</p>
<p>2. Previous Guidelines on the captioned subject issued vide circular DBOD.No.BP.BC.81/21.04.018/2014-15 dated March 31, 2015 are hereby superseded.</p>
<p>3. The revised guidelines shall be effective from April 1, 2025.</p>
<p>Yours faithfully,</p>
<p>(S. S. Mundra)<br>Deputy Governor</p>
"""

# 4. Master Direction with repealed circulars list
MASTER_DIRECTION_REPEALED_LIST_HTML = """
<p>July 08, 2025</p>
<p>All Scheduled Commercial Banks</p>
<p>Madam / Sir,</p>
<p><strong>Know Your Customer (KYC) Direction, 2016</strong></p>
<p>In exercise of the powers conferred by Section 35A of the Banking Regulation Act, 1949, the Reserve Bank of India hereby issues the directions contained herein.</p>
<p>2. These directions shall be known as the 'Know Your Customer (KYC) Direction, 2016'.</p>
<p>3. These directions shall come into force with immediate effect.</p>
<p><strong>Appendix</strong></p>
<p><strong>List of Circulars repealed</strong></p>
<table>
<tr><td>1.</td><td><a href="#">DBOD.No.Leg.BC.44/09.07.005/2002-03</a> dated November 25, 2002</td></tr>
<tr><td>2.</td><td><a href="#">DBOD.AML.BC.No.21/14.01.001/2004-05</a> dated August 18, 2004</td></tr>
<tr><td>3.</td><td><a href="#">DBOD.AML.BC.No.86/14.01.001/2005-06</a> dated June 29, 2006</td></tr>
</table>
<p>Yours faithfully,</p>
<p>(Deepak Singhal)<br>Chief General Manager</p>
"""

# 5. Acquisition & Transfer Master Circular with sunset
ACQUISITION_TRANSFER_SUNSET_HTML = """
<p>July 08, 2025</p>
<p>All Scheduled Commercial Banks</p>
<p>Madam / Sir,</p>
<p><strong>Master Circular on Acquisition and Transfer of Shares in Banks</strong></p>
<p>This Master Circular consolidates instructions on acquisition and transfer of shares in banks issued up to June 30, 2025.</p>
<p>2. This Master Circular has a sunset clause of one year and shall cease to be operative from July 1, 2026.</p>
<p>3. All banks are advised to ensure compliance with these guidelines.</p>
<p>Yours faithfully,</p>
<p>(R. Gandhi)<br>Deputy Governor</p>
"""

class TestEnhancedNotificationProcessing:
    """Test cases for enhanced notification processing"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mock_processor = Mock()
        
    def test_extract_anchor_information_amendment(self):
        """Test anchor extraction for amendment notifications"""
        from inuse_rss_feed_etl_dag import extract_anchor_information
        
        anchor_info = extract_anchor_information(AMENDMENT_NOTIFICATION_HTML)
        
        # Should extract reference number from anchor
        assert len(anchor_info['reference_numbers']) > 0
        assert 'DPSS.CO.PD.No.892/02.14.003/2016-17' in anchor_info['reference_numbers']
        
        # Should extract RBI links
        assert len(anchor_info['rbi_links']) > 0
        assert any('rbi.org.in' in link for link in anchor_info['rbi_links'])
        
        # Should capture anchor text and href
        assert len(anchor_info['anchors']) > 0
        anchor = anchor_info['anchors'][0]
        assert 'text' in anchor
        assert 'href' in anchor
        
    def test_extract_anchor_information_withdrawal(self):
        """Test anchor extraction for withdrawal notifications"""
        from inuse_rss_feed_etl_dag import extract_anchor_information
        
        anchor_info = extract_anchor_information(WITHDRAWAL_NOTIFICATION_HTML)
        
        # Should extract reference number
        assert 'FEMA.23/2015-16' in anchor_info['reference_numbers']
        
        # Should extract RBI links
        assert len(anchor_info['rbi_links']) > 0
        
    def test_extract_anchor_information_repealed_list(self):
        """Test anchor extraction for master direction with repealed list"""
        from inuse_rss_feed_etl_dag import extract_anchor_information
        
        anchor_info = extract_anchor_information(MASTER_DIRECTION_REPEALED_LIST_HTML)
        
        # Should extract multiple reference numbers from the repealed list
        assert len(anchor_info['reference_numbers']) >= 3
        assert 'DBOD.No.Leg.BC.44/09.07.005/2002-03' in anchor_info['reference_numbers']
        assert 'DBOD.AML.BC.No.21/14.01.001/2004-05' in anchor_info['reference_numbers']
        assert 'DBOD.AML.BC.No.86/14.01.001/2005-06' in anchor_info['reference_numbers']
        
    @patch('inuse_rss_feed_etl_dag.openai_manager')
    def test_enhanced_notification_analysis_amendment(self, mock_openai):
        """Test enhanced notification analysis for amendment type"""
        from inuse_rss_feed_etl_dag import NotificationProcessor
        
        # Mock LLM response for amendment
        mock_response = {
            'category': 'Amendment',
            'confidence': 'high',
            'reasoning': 'Contains "reference is invited to" pattern with clear document reference',
            'affects_regulations': True,
            'requires_kb_update': True
        }
        
        mock_openai.with_key_rotation.return_value.choices = [Mock(message=Mock(parsed=mock_response))]
        
        processor = NotificationProcessor()
        result = processor.analyze_notification(
            "Aadhaar-based Authentication for Card Present Transactions",
            AMENDMENT_NOTIFICATION_HTML,
            "https://rbi.org.in/notification/123"
        )
        
        assert result['category'] == 'Amendment'
        assert result['confidence'] == 'high'
        assert result['requires_kb_update'] == True
        
    @patch('inuse_rss_feed_etl_dag.openai_manager')
    def test_enhanced_document_extraction_withdrawal(self, mock_openai):
        """Test enhanced document extraction for withdrawal notifications"""
        from inuse_rss_feed_etl_dag import NotificationProcessor
        
        # Mock LLM response for document extraction
        mock_response = {
            'document_actions': [{
                'document_id': 'FEMA.23/2015-16',
                'action_type': 'REMOVE_DOCUMENT',
                'confidence': 'high',
                'reference_number': 'FEMA.23/2015-16',
                'original_date': '2015-07-01',
                'sunset_withdraw_date': '2026-07-01',
                'reasoning': 'Sunset clause detected with withdrawal date'
            }],
            'requires_manual_review': False
        }
        
        mock_openai.with_key_rotation.return_value.choices = [Mock(message=Mock(parsed=mock_response))]
        
        processor = NotificationProcessor()
        result = processor.extract_affected_documents(
            "Master Circular on Import of Goods and Services",
            WITHDRAWAL_NOTIFICATION_HTML,
            "Repealed/Withdrawn"
        )
        
        actions = result['document_actions']
        assert len(actions) > 0
        assert actions[0]['action_type'] == 'REMOVE_DOCUMENT'
        assert actions[0]['confidence'] == 'high'
        assert 'sunset_withdraw_date' in actions[0]

    @patch('inuse_rss_feed_etl_dag.openai_manager')
    def test_enhanced_document_extraction_repealed_list(self, mock_openai):
        """Test enhanced document extraction for master direction with repealed list"""
        from inuse_rss_feed_etl_dag import NotificationProcessor

        # Mock LLM response for multiple repealed documents
        mock_response = {
            'document_actions': [
                {
                    'document_id': 'DBOD.No.Leg.BC.44/09.07.005/2002-03',
                    'action_type': 'REMOVE_DOCUMENT',
                    'confidence': 'high',
                    'reference_number': 'DBOD.No.Leg.BC.44/09.07.005/2002-03',
                    'original_date': '2002-11-25',
                    'reasoning': 'Listed in repealed circulars appendix'
                },
                {
                    'document_id': 'DBOD.AML.BC.No.21/14.01.001/2004-05',
                    'action_type': 'REMOVE_DOCUMENT',
                    'confidence': 'high',
                    'reference_number': 'DBOD.AML.BC.No.21/14.01.001/2004-05',
                    'original_date': '2004-08-18',
                    'reasoning': 'Listed in repealed circulars appendix'
                },
                {
                    'document_id': 'DBOD.AML.BC.No.86/14.01.001/2005-06',
                    'action_type': 'REMOVE_DOCUMENT',
                    'confidence': 'high',
                    'reference_number': 'DBOD.AML.BC.No.86/14.01.001/2005-06',
                    'original_date': '2006-06-29',
                    'reasoning': 'Listed in repealed circulars appendix'
                }
            ],
            'requires_manual_review': False
        }

        mock_openai.with_key_rotation.return_value.choices = [Mock(message=Mock(parsed=mock_response))]

        processor = NotificationProcessor()
        result = processor.extract_affected_documents(
            "Know Your Customer (KYC) Direction, 2016",
            MASTER_DIRECTION_REPEALED_LIST_HTML,
            "New_Regulatory_Issuance"
        )

        actions = result['document_actions']
        assert len(actions) == 3  # Should extract all three repealed documents

        # All should be REMOVE_DOCUMENT actions with high confidence
        for action in actions:
            assert action['action_type'] == 'REMOVE_DOCUMENT'
            assert action['confidence'] == 'high'
            assert action['reference_number']  # Should have reference numbers
            assert action['original_date']  # Should have original dates

    def test_validation_and_enhancement_missing_reference(self):
        """Test validation logic for missing reference numbers"""
        from inuse_rss_feed_etl_dag import NotificationProcessor

        processor = NotificationProcessor()

        # Mock result with missing reference number
        mock_result = {
            'document_actions': [{
                'document_id': 'Some Document',
                'action_type': 'UPDATE_DOCUMENT',
                'confidence': 'high',
                'reference_number': '',  # Missing reference
                'original_date': '2025-01-01'
            }],
            'requires_manual_review': False
        }

        # Mock anchor info with available reference
        mock_anchor_info = {
            'reference_numbers': ['DBOD.No.Test.123/01.02.003/2025-26'],
            'rbi_links': [],
            'anchors': [],
            'document_links': []
        }

        enhanced_result = processor._validate_and_enhance_document_actions(
            mock_result, mock_anchor_info, "Test notification"
        )

        # Should enhance with reference from anchor info
        action = enhanced_result['document_actions'][0]
        assert action['reference_number'] == 'DBOD.No.Test.123/01.02.003/2025-26'
        assert action['confidence'] == 'medium'  # Downgraded due to enhancement

    def test_validation_and_enhancement_confidence_upgrade(self):
        """Test confidence upgrade for pattern matches"""
        from inuse_rss_feed_etl_dag import NotificationProcessor

        processor = NotificationProcessor()

        # Mock result with complete data
        mock_result = {
            'document_actions': [{
                'document_id': 'DBOD.No.Test.123/01.02.003/2025-26',
                'action_type': 'UPDATE_DOCUMENT',
                'confidence': 'medium',
                'reference_number': 'DBOD.No.Test.123/01.02.003/2025-26',
                'original_date': '2025-01-01'
            }],
            'requires_manual_review': False
        }

        mock_anchor_info = {
            'reference_numbers': [],
            'rbi_links': [],
            'anchors': [],
            'document_links': []
        }

        # Title with pattern match keywords
        title = "Reference is invited to the circular on banking regulations"

        enhanced_result = processor._validate_and_enhance_document_actions(
            mock_result, mock_anchor_info, title
        )

        # Should upgrade confidence to high due to pattern match
        action = enhanced_result['document_actions'][0]
        assert action['confidence'] == 'high'
        assert action['validation_status'] == 'passed'

    def test_validation_blocking_for_critical_missing_fields(self):
        """Test validation blocking for critical missing fields"""
        from inuse_rss_feed_etl_dag import NotificationProcessor

        processor = NotificationProcessor()

        # Mock result with missing critical fields
        mock_result = {
            'document_actions': [{
                'document_id': 'Some Document',
                'action_type': 'REMOVE_DOCUMENT',
                'confidence': 'high',
                'reference_number': '',  # Missing critical field
                'original_date': ''  # Missing critical field
            }],
            'requires_manual_review': False
        }

        mock_anchor_info = {
            'reference_numbers': [],  # No anchor info to help
            'rbi_links': [],
            'anchors': [],
            'document_links': []
        }

        enhanced_result = processor._validate_and_enhance_document_actions(
            mock_result, mock_anchor_info, "Test notification"
        )

        # Should flag for manual review and downgrade confidence
        assert enhanced_result['requires_manual_review'] == True
        action = enhanced_result['document_actions'][0]
        assert action['confidence'] == 'low'
        assert action['validation_status'] == 'issues_found'
        assert 'Missing reference_number' in action['validation_notes']
        assert 'Missing original_date' in action['validation_notes']

if __name__ == "__main__":
    pytest.main([__file__])
