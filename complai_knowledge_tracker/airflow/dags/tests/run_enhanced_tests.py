#!/usr/bin/env python3
"""
Test runner for enhanced notification processing pipeline
Validates the five key notification types and enhanced features
"""

import sys
import os
import logging
from pathlib import Path

# Add the parent directory to the path so we can import the DAG modules
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_anchor_extraction():
    """Test the enhanced anchor extraction functionality"""
    logger.info("🔍 Testing anchor extraction functionality...")
    
    try:
        from inuse_rss_feed_etl_dag import extract_anchor_information
        
        # Test HTML with embedded reference numbers
        test_html = """
        <p>A reference is invited to the circular <a href="https://rbi.org.in/doc123">DBOD.No.Leg.BC.21/09.07.007/2002-03</a> dated March 15, 2003.</p>
        <p>Please also refer to <a href="https://rbi.org.in/doc456">RBI/2024-25/134</a> for additional guidance.</p>
        """
        
        anchor_info = extract_anchor_information(test_html)
        
        # Validate results
        assert len(anchor_info['anchors']) >= 2, f"Expected at least 2 anchors, got {len(anchor_info['anchors'])}"
        assert len(anchor_info['reference_numbers']) >= 2, f"Expected at least 2 references, got {len(anchor_info['reference_numbers'])}"
        assert 'DBOD.No.Leg.BC.21/09.07.007/2002-03' in anchor_info['reference_numbers'], "Missing expected reference number"
        assert 'RBI/2024-25/134' in anchor_info['reference_numbers'], "Missing expected RBI reference"
        assert len(anchor_info['rbi_links']) >= 2, f"Expected at least 2 RBI links, got {len(anchor_info['rbi_links'])}"
        
        logger.info("✅ Anchor extraction test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Anchor extraction test failed: {e}")
        return False

def test_pattern_recognition():
    """Test pattern recognition in notification content"""
    logger.info("🔍 Testing pattern recognition...")
    
    try:
        from inuse_rss_feed_etl_dag import extract_anchor_information
        
        # Test different notification patterns
        test_cases = [
            {
                'name': 'Amendment Pattern',
                'html': '<p>A reference is invited to the circular DBOD.No.123/2025-26 dated January 1, 2025.</p>',
                'expected_refs': ['DBOD.No.123/2025-26']
            },
            {
                'name': 'Withdrawal Pattern',
                'html': '<p>The circular FEMA.23/2015-16 will stand withdrawn on July 1, 2026.</p>',
                'expected_refs': ['FEMA.23/2015-16']
            },
            {
                'name': 'Multiple References',
                'html': '''
                <p>List of Circulars repealed:</p>
                <p>1. DBOD.No.Leg.BC.44/09.07.005/2002-03 dated November 25, 2002</p>
                <p>2. DBOD.AML.BC.No.21/14.01.001/2004-05 dated August 18, 2004</p>
                ''',
                'expected_refs': ['DBOD.No.Leg.BC.44/09.07.005/2002-03', 'DBOD.AML.BC.No.21/14.01.001/2004-05']
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"   Testing {test_case['name']}...")
            anchor_info = extract_anchor_information(test_case['html'])
            
            for expected_ref in test_case['expected_refs']:
                assert expected_ref in anchor_info['reference_numbers'], \
                    f"Missing expected reference {expected_ref} in {test_case['name']}"
            
            logger.info(f"   ✅ {test_case['name']} passed")
        
        logger.info("✅ Pattern recognition test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Pattern recognition test failed: {e}")
        return False

def test_validation_logic():
    """Test the validation and enhancement logic"""
    logger.info("🔍 Testing validation logic...")
    
    try:
        # Mock the NotificationProcessor for testing
        class MockNotificationProcessor:
            def _validate_and_enhance_document_actions(self, result, anchor_info, title):
                # Simplified validation logic for testing
                document_actions = result.get('document_actions', [])
                enhanced_actions = []
                validation_issues = []
                
                for action in document_actions:
                    enhanced_action = dict(action)
                    action_type = action.get('action_type', '')
                    reference_number = action.get('reference_number', '')
                    
                    # Validation Gate: Critical fields for UPDATE/REMOVE actions
                    if action_type in ['UPDATE_DOCUMENT', 'REMOVE_DOCUMENT']:
                        if not reference_number:
                            if anchor_info['reference_numbers']:
                                enhanced_action['reference_number'] = anchor_info['reference_numbers'][0]
                                enhanced_action['confidence'] = 'medium'
                            else:
                                validation_issues.append(f"Missing reference_number for {action_type}")
                                enhanced_action['confidence'] = 'low'
                    
                    enhanced_action['validation_status'] = 'passed' if not validation_issues else 'issues_found'
                    enhanced_actions.append(enhanced_action)
                
                enhanced_result = dict(result)
                enhanced_result['document_actions'] = enhanced_actions
                if validation_issues:
                    enhanced_result['requires_manual_review'] = True
                
                return enhanced_result
        
        processor = MockNotificationProcessor()
        
        # Test case 1: Missing reference number with anchor info available
        result1 = {
            'document_actions': [{
                'document_id': 'Test Document',
                'action_type': 'UPDATE_DOCUMENT',
                'confidence': 'high',
                'reference_number': ''
            }]
        }
        
        anchor_info1 = {
            'reference_numbers': ['DBOD.No.Test.123/2025-26'],
            'rbi_links': [],
            'anchors': [],
            'document_links': []
        }
        
        enhanced1 = processor._validate_and_enhance_document_actions(result1, anchor_info1, "Test")
        action1 = enhanced1['document_actions'][0]
        
        assert action1['reference_number'] == 'DBOD.No.Test.123/2025-26', "Should enhance with anchor reference"
        assert action1['confidence'] == 'medium', "Should downgrade confidence when enhanced"
        
        # Test case 2: Missing reference number with no anchor info
        result2 = {
            'document_actions': [{
                'document_id': 'Test Document',
                'action_type': 'REMOVE_DOCUMENT',
                'confidence': 'high',
                'reference_number': ''
            }]
        }
        
        anchor_info2 = {
            'reference_numbers': [],
            'rbi_links': [],
            'anchors': [],
            'document_links': []
        }
        
        enhanced2 = processor._validate_and_enhance_document_actions(result2, anchor_info2, "Test")
        
        assert enhanced2['requires_manual_review'] == True, "Should flag for manual review"
        assert enhanced2['document_actions'][0]['confidence'] == 'low', "Should downgrade confidence"
        
        logger.info("✅ Validation logic test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Validation logic test failed: {e}")
        return False

def run_all_tests():
    """Run all enhanced notification processing tests"""
    logger.info("🚀 Starting enhanced notification processing tests...")
    
    tests = [
        test_anchor_extraction,
        test_pattern_recognition,
        test_validation_logic
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    logger.info(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        logger.info("🎉 All tests passed! Enhanced notification processing pipeline is ready.")
        return True
    else:
        logger.error("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
